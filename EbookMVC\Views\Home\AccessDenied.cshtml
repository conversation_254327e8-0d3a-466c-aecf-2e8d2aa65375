@{
    ViewData["Title"] = "Access Denied";
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title"><i class="fas fa-exclamation-triangle me-2"></i>Access Denied</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-5x text-danger mb-3"></i>
                        <h4>Bạn không có quyền truy cập trang này</h4>
                        <p class="text-muted">Vui lòng liên hệ quản trị viên nếu bạn cần hỗ trợ.</p>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>V<PERSON> trang chủ
                        </a>
                        <form id="logoutForm" class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                            
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>