@model IEnumerable<EbookMVC.Models.UserLog>

@{
    ViewData["Title"] = "Nhật ký hoạt động";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin/Home">Home</a></li>
                    <li class="breadcrumb-item active">Nhật ký hoạt động</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Danh sách hoạt động</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="remove">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Hành động</th>
                                    <th>Đối tượng</th>
                                    <th>ID đối tượng</th>
                                    <th>Mô tả</th>
                                    <th>IP</th>
                                    <th>Người dùng</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>
                                            @if (item.Action == "CREATE")
                                            {
                                                <span class="badge bg-success">Tạo mới</span>
                                            }
                                            else if (item.Action == "UPDATE")
                                            {
                                                <span class="badge bg-warning">Cập nhật</span>
                                            }
                                            else if (item.Action == "DELETE")
                                            {
                                                <span class="badge bg-danger">Xóa</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-info">@item.Action</span>
                                            }
                                        </td>
                                        <td>@item.EntityName</td>
                                        <td>@item.EntityId</td>
                                        <td>@item.Description</td>
                                        <td>@item.IpAddress</td>
                                        <td>@item.UserName</td>
                                        <td>@item.Timestamp.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

@section Scripts {
    <script>
        $(function () {
            $('table').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "order": [[7, 'desc']], // Sắp xếp theo thời gian giảm dần
                "info": true,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
}
