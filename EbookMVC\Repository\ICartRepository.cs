using EbookMVC.Models;

namespace EbookMVC.Repository
{
    public interface ICartRepository
    {
        // Cart operations
        Cart GetCartByUserId(string userId);
        Cart GetCartById(int cartId);
        void CreateCart(Cart cart);
        void UpdateCart(Cart cart);
        void DeleteCart(int cartId);

        // CartItem operations
        CartItem GetCartItem(int cartId, int productId);
        void AddCartItem(CartItem cartItem);
        void UpdateCartItem(CartItem cartItem);
        void RemoveCartItem(int cartItemId);
        void RemoveCartItem(int cartId, int productId);
        void ClearCart(int cartId);

        // Helper methods
        int GetCartItemCount(string userId);
        decimal GetCartTotal(string userId);
        List<CartItem> GetCartItems(string userId);
    }
}
