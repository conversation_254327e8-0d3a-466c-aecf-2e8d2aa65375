@page
@model ForgotPasswordConfirmation
@{
    ViewData["Title"] = "Xác nhận quên mật khẩu";
}

<div class="login-box">
    <div class="card card-outline card-primary">
        <div class="card-header">
            <a href="@Url.Action("Index", "Home")" class="link-dark text-center link-offset-2 link-opacity-100 link-opacity-50-hover">
                <h1 class="mb-0"><b>E</b>learn</h1>
            </a>
        </div>
        <div class="card-body login-card-body">
            <p class="login-box-msg">
                Vui lòng kiểm tra email của bạn để đặt lại mật khẩu.
            </p>
            <div class="text-center">
                <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
            </div>
            <p class="text-center mt-3">
                Chúng tôi đã gửi hướng dẫn đặt lại mật khẩu đến email của bạn.
            </p>
            <p class="mb-1 mt-3 text-center">
                <a href="@Url.Page("./Login")" class="btn btn-primary">Quay lại đăng nhập</a>
            </p>
        </div>
    </div>
</div>
