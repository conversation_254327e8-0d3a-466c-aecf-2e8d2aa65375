@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;700&family=Forum&display=swap");

:root{
  --primary-color: #075cdb;
  --secondary: #dbfffe;
  --black: #141414;
  --white: #fff;
  --box-shadow: 0 .5rem 1rem rgba(0, 0, 0, 0.1);
}

*{
  font-family: 'DM Sans', sans-serif ;
  margin: 0; padding: 0;
  box-sizing: border-box;
  outline: none; border: none;
  text-decoration: none;
  text-transform: capitalize;
  transition: all .2s linear;
}

html{
  font-size: 62.5%;
  overflow-x: hidden;
  scroll-padding-top: 9rem;
  scroll-behavior: smooth;
}

html::-webkit-scrollbar{
  width: 1rem;
}

html::-webkit-scrollbar-track{
  background: transparent;
}

html::-webkit-scrollbar-thumb{
  background: var(--primary-color);
}

section{
  padding: 5rem 7%;
}

.heading{
  position: relative;
  color: var(--primary-color);
  font-size: 3rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: .4rem;
  text-align: center;
  margin-bottom: 3rem;
}

.heading::after{
  /* content: url('../images/separator.svg'); */
  display: block;
  width: 10rem;
  margin-inline: auto;
  margin-top: -1rem;
}

.btn{
  size: 30px;
  position: relative;
  display: inline-block;
  margin-top: 1rem;
  color: var(--primary-color);
  font-size: 1.4rem;
  font-weight: 700;
  text-transform: uppercase;
  max-width: max-content;
  border: .1rem solid var(--primary-color);
  background: var(--secondary);
  padding: 1.2rem 4.5rem;
  overflow: hidden;
  z-index: 1;
  border-radius: .5rem;
}

.btn::before{
  content: "";
  position: absolute;
  bottom: 100%; left: 50%;
  transform: translateX(-50%);
  width: 200%; height: 200%;
  border-radius: 50%;
  background-color: var(--primary-color);
  transition: 500ms ease;
  z-index: -1;
}

.btn .text{
  transition: 250ms ease;
}

/* .btn .text-2{
  position: absolute;
  top: 100%; left: 50%;
  transform: translateX(-50%);
  min-width: max-content;
  color: var(--secondary);
} */

.btn:is(:hover, :focus-visible)::before{
  bottom: -50%;
}

.btn:is(:hover, :focus-visible) .text-1{
  transform: translateY(-4rem);
}

/* .btn:is(:hover, :focus-visible) .text-2{
  top: 50%;
  transform: translate(-50%, -50%);
} */

.shine{
  position: relative;
}

.shine::after{
  content: "";
  position: absolute;
  top: 0; left: 0; 
  width: 50%;
  height: 100%;
  background-image: linear-gradient(to right, transparent 0%, #fff6 100%);
  transform: skewX(-0.08turn) translateX(-180%);
}

.shine:is(:hover, :focus-within)::after{
  transform: skewX(-0.08turn) translateX(275%);
  transition: 1000ms ease;
}

/* header */

.header{
  position: fixed;
  top: 0; left: 0; right: 0;
  z-index: 1000;
  background: var(--white);
  box-shadow: var(--box-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 9%;
}
/* Tắt gạch chân cho link ở header */
.header a{
  text-decoration: none; 
}
.header .logo{
  font-size: 2.5rem;
  font-weight: bolder;
  color: var(--black);
}

.header .logo i{
  color: var(--primary-color);
  padding-right: .5rem;
}

.header .navbar a{
  font-size: 1.7rem;
  color: var(--black);
  margin: 0 1rem;
  padding-block: 1rem;
}

.header .navbar a:hover{
  color: var(--primary-color);
}

.header .navbar .hover-underline{
  position: relative;
  max-width: max-content;
}

.header .navbar .hover-underline::after{
  content: '';
  position: absolute;
  left: 0; bottom: 0;
  width: 100%;
  height: .5rem;
  border-block: .1rem solid var(--primary-color);
  transform: scaleX(0.2);
  opacity: 0;
  transition: 500ms ease;
}

.header .navbar .hover-underline:is(:hover, :focus-visible)::after{
  transform: scaleX(1);
  opacity: 1;
}

.header .icons div{
  font-size: 2.5rem;
  margin-left: 1.7rem;
  color: var(--black);
  cursor: pointer;
}

.header .icons div:hover{
  color: var(--primary-color);
}

#menu-btn {
    display: inline-block;
    font-size: 2.5rem;
    color: var(--black);
    cursor: pointer;
}

.header .login-form{
  position: absolute;
  top: 115%; right: -105%;
  background: var(--white);
  box-shadow: var(--box-shadow);
  width: 35rem;
  box-shadow: var(--box-shadow);
  padding: 2rem;
  border: 0.2rem solid var(--primary-color); /* Thêm viền màu primary */
  border-radius: 0.5rem; /* Bo góc viền */
}

.header .login-form.active{
  right: 2rem;
  transition: .4s linear;
}

.header .login-form h3{
  font-size: 2.2rem;
  color: var(--black);
  text-align: center;
  margin-bottom: .7rem;
}

.header .login-form .box{
  font-size: 1.5rem;
  margin: .7rem 0;
  border: .1rem solid rgba(0, 0, 0, 0.1);
  padding: 1rem 1.2rem;
  color: var(--black);
  text-transform: none;
  width: 100%;
}

.header .login-form .remember{
  display: flex;
  align-items: center;
  gap: .5rem;
  margin: .7rem 0;
}

.header .login-form .remember label{
  font-size: 1.5rem;
  color: var(--black);
  cursor: pointer;
}

/* home */

.trang-chu{
  min-height: 70vh;
  display: flex;
  align-items: center;
  background: url(../images/home-1.jpg) no-repeat;

  background-size: cover;
  background-position: center;
}


.trang-chu .content{
  max-width: 50rem;
}

.trang-chu .content h3{
  font-size: 5rem;
  color: var(--primary-color);  
  line-height: 1.2;
}

.trang-chu .content p{
  font-size: 1.5rem;
  line-height: 2;
  color: var(--black);  
  padding: 1rem 0;
}

/* ends */

/* about */

.gioi-thieu .container{
  display: flex;
  flex-wrap: wrap;
}

.gioi-thieu .container .gioi-thieu-image{
  position: relative;
  flex: 1 1 40rem;
}

.gioi-thieu .container .gioi-thieu-image img{
  width: 80%;
}

.gioi-thieu .container .gioi-thieu-image .gioi-thieu-img{
  position: absolute;
  bottom: -10rem;
  right: -1rem;
  width: 25rem;
  padding-block: 5rem;
}

.gioi-thieu .container .gioi-thieu-content{
  flex: 1 1 50rem;
  padding-left: 6rem;
  padding-top: 8rem;
}

.gioi-thieu .container .gioi-thieu-content h3{
  font-size: 3rem;
  color: var(--primary-color);
}

.gioi-thieu .container .gioi-thieu-content p{
  font-size: 1.6rem;
  color: #444;
  padding: 1rem 0;
  line-height: 1.8;
}

/* ends */

/* subjects */

.subjects .box-container{
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
}

.subjects .box-container .box{
  padding: 3rem 2rem;
  text-align: center;
  border: .1rem solid var(--primary-color);
  background: var(--secondary);
  cursor: pointer;
}

.subjects .box-container .box:hover{
  background: var(--primary-color);
}

.subjects .box-container .box img{
  height: 10rem;
  margin-bottom: .7rem;
}

.subjects .box-container .box h3{
  font-size: 1.7rem;
  font-weight: bolder;
  color: #444;
  padding: .5rem 0;
  text-transform: capitalize;
}

.subjects .box-container .box:hover h3{
  color: #fff;
}

.subjects .box-container .box p{
  font-size: 1.5rem;
  line-height: 2;
  color: #777;
}

.subjects .box-container .box:hover p{
  color: #eee;
}

/* end */

/* khoa-hoc */
.khoa-hoc .box-container .box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%; /* Đảm bảo các box có chiều cao bằng nhau */
  border-radius: 1rem; /* Bo góc các box */
  overflow: hidden;
  box-shadow: var(--box-shadow);
  border: 0.1rem solid var(--primary-color); /* Thêm viền màu primary */
}

.khoa-hoc .box-container .box .content {
  flex-grow: 1; /* Đảm bảo nội dung lấp đầy không gian còn lại */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.khoa-hoc .box-container{
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 2rem;
}

.khoa-hoc .box-container .box .image{
  height: 25rem;
  overflow: hidden;
  position: relative;
}

.khoa-hoc .box-container .box .image img{
  height: 100%;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

.khoa-hoc .box-container .box:hover .image img{
  transform: scale(1.1);
}

.khoa-hoc .box-container .box .image h3{
  font-size: 1.5rem;
  font-weight: bolder !important;
  color: #444;
  position: absolute;
  top: 1rem; left: 1rem;
  padding: .5rem 1.5rem;
  background: var(--white);
}

.khoa-hoc .box-container .box .content{
  padding: 2rem;
  position: relative;
  border: 0.1rem solid var(--primary-color);
}

.khoa-hoc .box-container .box .content h4{
  position: absolute;
  background: var(--primary-color);
  color: white;
  padding: .5rem 1rem;
  top: -3rem; right: 1.5rem;
  padding: 1rem;
  text-align: center;
  font-size: 1.4rem;
  border-radius: 50%;
  white-space: nowrap;
}

.khoa-hoc .box-container .box .content p{
  font-size: 1.4rem;
  color: #444;
  line-height: 2;
}

.khoa-hoc .box-container .box .content h3{
  font-size: 2rem;
  color: #444;
}

.khoa-hoc .box-container .box .content .stars{
  padding: 1rem 0;
}

.khoa-hoc .box-container .box .content .stars i{
  font-size: 1.7rem;
  color: var(--primary-color);
}

.khoa-hoc .box-container .box .content .stars span{
  font-size: 1.4rem;
  color: #777;
  margin-left: .5rem;
}

.khoa-hoc .box-container .box .content .icons{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 0.1rem solid var(--primary-color);
}

.khoa-hoc .box-container .box .content .icons span{
  font-size: 1.5rem;
  color: #444;
}

.khoa-hoc .box-container .box .content .icons span i{
  color: var(--primary-color);
  padding-right: .5rem;
}

/* ends */

/* teacher */

.teacher .box-container{
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
}

.teacher .box-container .box{
  background: var(--white);
  box-shadow: var(--box-shadow);
  border-radius: .5rem;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.teacher .box-container .box .image{
  position: relative;
  overflow: hidden;
}

.teacher .box-container .box .image img{
  background: var(--secondary);
  width: 100%;
}

.teacher .box-container .box:hover .image .share{
  bottom: 0;
}

.teacher .box-container .box .image .share{
  position: absolute;
  bottom: -10rem; 
  left: 0; right: 0;
  padding: 2rem;
}

.teacher .box-container .box .image .share a{
  height: 4rem;
  width: 4rem;
  line-height: 4rem;
  font-size: 2rem;
  margin: 0 1rem;
  color: var(--secondary);
  background: var(--primary-color);
  border-radius: .5rem;
}

.teacher .box-container .box .image .share a:hover{
  color: var(--primary-color);
  background: var(--secondary);
}

.teacher .box-container .box .content{
  padding: 2rem;
  padding-top: 1rem;
  align-items: center;
}

.teacher .box-container .box .content h3{
  font-size: 2rem;
  color: var(--black);
}

.teacher .box-container .box .content span{
  font-size: 1.5rem;
  line-height: 2;
  color: #777;
}

/* ends */

/* review */

.review .slide{
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  border: 0.1rem solid rgba(0, 0, 0, 0.1);
  border-radius: 2rem;
  position: relative;
}

.review .slide p{
  font-size: 1.5rem;
  text-align: center;
  padding-bottom: .5rem;
  line-height: 1.8;
  color: #444;
}

.review .slide .wrapper{
  display: flex;
  justify-content: center;
  gap: .3rem;
}

.review .slide .wrapper .separator{
  width: .8rem;
  height: .8rem;
  border: .1rem solid var(--primary-color);
  transform: rotate(45deg);
  animation: rotate360 15s linear infinite;
}

@keyframes rotate360{
  0%{
    transform: rotate(0);
  }
  100%{
    transform: rotate(1turn);
  }
}

.review .slide .fa-quote-right{
  position: absolute;
  bottom: 3rem; right: 3rem;
  font-size: 6rem;
  color: var(--secondary);
}

.review .slide .user{
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 1.5rem;
  border-radius: .5rem;
}

.review .slide .user img{
  height: 7rem;
  width: 7rem;
  border-radius: 15%;
  object-fit: cover;
  border: .1rem solid var(--primary-color);
  padding: .5rem;
}

.review .slide .user h3{
  font-size: 2rem;
  color: var(--black);
  padding-bottom: .5rem;
}

.review .slide .user .stars i{
  font-size: 1.3rem;
  color: var(--primary-color);
}

/* ends */

/* blog */

.blog .box-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr)); /* Đảm bảo các ô tự động điều chỉnh kích thước */
  gap: 1.5rem; /* Khoảng cách giữa các ô */
  margin: 0 auto; /* Căn giữa các ô trong khung */
  padding: 0; /* Khoảng cách bên trong khung */
}

.blog .box-container .box {
  height: auto; /* Đảm bảo chiều cao tự động theo nội dung */
  min-height: 25rem; /* Đặt chiều cao tối thiểu để tránh bị hở */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: var(--box-shadow);
  border-radius: 2rem;
  overflow: hidden;
}

.blog .box-container .box .image{
  height: 25rem;
  width: 100%;
  overflow: hidden;
  position: relative;
  border-radius: 2rem 2rem 0 0;
}

.blog .box-container .box .image img{
  width: 100%; /* Đảm bảo chiều rộng đầy đủ */
  height: 100%; /* Giữ tỷ lệ hình ảnh */
  object-fit: cover; /* Đảm bảo hình ảnh lấp đầy không gian mà không bị méo */

  transition: transform 0.3s ease-in-out;
}

.blog .box-container .box:hover .image img{
  transform: scale(1.1);
}

.blog .box-container .box .image h3{
  font-size: 1.5rem;
  color: #444;
  position: absolute;
  top: 1.5rem; left: 1rem;
  padding: .5rem 1.5rem;
  background: var(--white);
  border-radius: 2rem 0 2rem 0;
}

.blog .box-container .box .content{
  background: var(--secondary);
  border-radius: 0 0 2rem 2rem;
  padding: 2rem; /* Khoảng cách bên trong */
  flex-grow: 1; /* Đảm bảo nội dung lấp đầy không gian còn lại */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.blog .box-container .box .content .icons{
  margin-bottom: 1rem;
  align-items: center;
}

.blog .box-container .box .content .icons a{
  font-size: 1.4rem;
  color: var(--primary-color);
}

.blog .box-container .box .content .icons a i{
  padding-right: .5rem;
}

.blog .box-container .box .content h3{
  font-size: 2.2rem;
  color: var(--black);
}

.blog .box-container .box .content p{
  font-size: 1.5rem;
  color: var(--black);
  line-height: 2;
  padding: 1rem 0;
}



/* ends */

/* contact */

.contact .row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
}

.contact .row .image{
  flex: 1 1 40rem;
}

.contact .row .image img{
  width: 100%;
}

.contact .row form{
  flex: 1 1 40rem;
  border: 0.1rem solid var(--primary-color);
  padding: 2rem;

}

.contact .row form h3{
  font-size: 2.5rem;
  color: #444;
  padding-bottom: 1rem;
}

.contact .row form .box{
  width: 100%;
  font-size: 1.6rem;
  padding: 1.2rem 1.4rem;
  border: .1rem solid var(--primary-color);
  margin: .7rem 0;
  text-transform: none;
}

.contact .row form .box:focus{
  color: var(--primary-color);
}

.contact .row form textarea{
  height: 15rem;
  resize: none;
}

/* ends */

/* footer */

.footer{
  background: var(--secondary);
  text-align: center;
}

.footer .box-container{
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
}

.footer .box-container .box h3{
  font-size: 2.2rem;
  color: var(--primary-color);
  padding: 1rem 0;
  
}
.footer .box-container .box p{
  font-size: 1.5rem;
  color: var(--black);
  line-height: 2;
  padding: 1rem 0;
  text-transform: none;
}

.footer .box-container .box .share{
  margin-top: 1rem;
}

.footer .box-container .box .share a{
  height: 4.5rem;
  width: 4.5rem;
  line-height: 4.5rem;
  border-radius: 10%;
  font-size: 1.7rem;
  border: .1rem solid var(--primary-color);
  color: var(--primary-color);
  margin-right: .3rem;
  text-align: center;
}

.footer .box-container .box .share a:hover{
  background: var(--primary-color);
  color: var(--white);
}

.footer .box-container .box .link{
  font-size: 1.7rem;
  line-height: 2;
  color: var(--black);
  padding: .5rem 0;
  display: block;
}

.footer .box-container .box .link:hover{
  color: var(--primary-color);
  text-decoration: underline;
}

.footer .credit{
  font-size: 2rem;
  margin-top: 3rem;
  padding-top: 3rem;
  text-align: center;
  color: var(--black);
  border-top: .1rem solid var(--primary-color);
}

.footer .credit span{
  color: var(--primary-color);
}

/* ends */

/* media queries */

@media (max-width:991px){

  html{
    font-size: 55%;
  }

  .header{
    padding: 2rem 4rem;
  }

  section{
    padding: 3rem 2rem;
  }

}

@media (max-width:768px){

  #menu-btn{
    display: inline-block;
  }

  .header .navbar{
    position: absolute;
    top: 110%; right: -110%;
    width: 30rem;
    box-shadow: var(--box-shadow);
    border-radius: .5rem;
    background: var(--white);
  }

  .header .navbar.active{
    right: 2rem;
    transition: .4s linear;
  }

  .header .navbar a{
    font-size: 2rem;
    margin: 1rem 2.5rem;
    display: block;
  }

  .trang-chu .content h3{
    font-size: 3rem;
  }

  .gioi-thieu .container .gioi-thieu-content{
    padding-left: 0;
  }

}

@media (max-width:450px){
  html{
    font-size: 50%;
  }
}


/* Dark Mode Styles */
body.dark-mode {
    background-color: #282a36;
    color: #f8f8f2;
    transition: background-color 0.5s ease-in-out, color 0.5s ease-in-out;
}
.dark-mode .trang-chu{
  /* default light mode */
  background: url(../images/home-1.jpg) no-repeat;
  background-size: cover;
  background-position: center;
  filter: brightness(0.9); /* Giảm độ sáng của hình ảnh */
  transition: filter 0.5s ease-in-out; /* Thêm hiệu ứng chuyển tiếp */
  transition: background-color 0.5s ease-in-out;
}
/* Các thành phần khác trong dark mode */
.dark-mode .header,
.dark-mode .navbar,
.dark-mode .footer,
.dark-mode .navbar-expand-lg,
.dark-mode .navbar-light,
.dark-mode .navbar-light .navbar-nav .nav-link,
.dark-mode .bg-light,
.dark-mode .collapse {
    background-color: #44475a;
    color: #f8f8f2;
}
.dark-mode .header .logo{
    color: #f8f8f2;
}

.dark-mode a {
    color: #50fa7b;
}

.dark-mode .btn {
    background-color: #6272a4;
    color: #f8f8f2;
}

.dark-mode .box,  
.dark-mode .login-form {
    
    background-color: #44475a;
    color: #f8f8f2;

}

.dark-mode .login-form {
    background-color: #44475a; /* Nền tối */
    color: #f8f8f2; /* Màu chữ trắng */
    border: 0.2rem solid var(--primary-color); /* Viền màu primary */
}

.dark-mode .login-form h3 {
    color: #ffffff; /* Màu chữ trắng cho tiêu đề "Đăng nhập" */
}

.dark-mode .login-form .box {
    background-color: #44475a; /* Nền ô nhập */
    color: #f8f8f2; /* Màu chữ trắng */
    border: 0.1rem solid var(--primary-color); /* Viền màu primary */
}

.dark-mode .login-form .box::placeholder {
    color: #cccccc; /* Màu placeholder nhạt hơn */
}

.dark-mode .login-form .box:focus {
    color: #ffffff; /* Màu chữ trắng khi nhập */
    border-color: #50fa7b; /* Viền đổi màu khi focus */
    outline: none; /* Bỏ viền mặc định */
}


.dark-mode .gioi-thieu .container .gioi-thieu-content p{
    color: #ffffff; /* Màu chữ trắng trong chế độ Dark Mode */
}
.dark-mode .review .slide p{
    color: #ffffff; /* Màu chữ trắng trong chế độ Dark Mode */
}
.dark-mode .review .slide h3{
    color: var(--primary-color); /* Màu chữ trắng trong chế độ Dark Mode */
}.dark-mode .review .slide .user .user-info .stars i{
    color: #ffffff; /* Màu chữ trắng trong chế độ Dark Mode */
}
.review .slide {
  border: 0.3rem solid var(--primary-color); /* Viền nổi bật màu primary */
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  border-radius: 2rem;
  position: relative;
}

/* Nút nhấn chuyển dark mode */
.custom-btn {
    background-color: #007bff; /* Màu xanh */
    color: white; /* Màu chữ trắng */
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.custom-btn:hover {
    background-color: #0056b3; /* Màu xanh đậm hơn khi hover */
}

/* Dark Mode Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 25px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 19px;
    width: 19px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #075cdb;
}

input:checked + .slider:before {
    transform: translateX(25px);
}

.dark-mode .khoa-hoc .box-container .box .content h4{
  color: #f8f8f2; 
}
.dark-mode .khoa-hoc .box-container .box .content p{
  color: #f8f8f2; 
}.dark-mode .khoa-hoc .box-container .box .content h3{
  color: #f8f8f2; 
}
.dark-mode .khoa-hoc .box-container .box .content .icons span{
  color: #f8f8f2; 
}

.menu-toggle.open i {
    content: "\f00d"; /* Font Awesome icon for 'times' */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}

/* Nút menu */
.menu-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%; /* Bo tròn nút */
    cursor: pointer;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.menu-toggle:hover {
    background-color: #0056b3; /* Màu đậm hơn khi hover */
    transform: scale(1.1); /* Phóng to nhẹ khi hover */
}

.menu-toggle i {
    font-size: 2rem;
}


