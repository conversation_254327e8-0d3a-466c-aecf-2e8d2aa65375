@{
    if (ViewData.TryGetValue("ParentLayout", out var parentLayout))
    {
        Layout = (string)parentLayout;
    }
    else
    {
        Layout = "/Views/Shared/_Layout.cshtml";
    }
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-page="./Index">Quản lý tài khoản</a></li>
                    @if (ViewData["Title"]?.ToString() != "Thông tin cá nhân")
                    {
                        <li class="breadcrumb-item active">@ViewData["Title"]</li>
                    }
                    else
                    {
                        <li class="breadcrumb-item active">Thông tin cá nhân</li>
                    }
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <partial name="_ManageNav" />
            </div>
            <div class="col-md-9">
                @RenderBody()
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @RenderSection("Scripts", required: false)
}