@model EbookMVC.Models.ProductSearchViewModel

@{
    ViewData["Title"] = "Tìm kiếm khóa học";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a asp-action="Index">Quản lý khóa học</a></li>
                    <li class="breadcrumb-item active">Tìm kiếm</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Tìm kiếm khóa học</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <form asp-action="Search" method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Keyword">Từ khóa (tên, mô tả, danh mục, giá bán hoặc ID)</label>
                                        <input asp-for="Keyword" class="form-control" placeholder="Nhập từ khóa tìm kiếm...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="CategoryId">Danh mục</label>
                                        <select asp-for="CategoryId" asp-items="Model.Categories" class="form-control">
                                            <option value="">-- Tất cả danh mục --</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="MinPrice">Giá tối thiểu</label>
                                        <input asp-for="MinPrice" class="form-control" placeholder="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="MaxPrice">Giá tối đa</label>
                                        <input asp-for="MaxPrice" class="form-control" placeholder="10,000,000">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label asp-for="Level">Cấp độ</label>
                                        <select asp-for="Level" asp-items="Model.Levels" class="form-control">
                                            <option value="">-- Tất cả cấp độ --</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Tìm kiếm
                                </button>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Quay lại
                                </a>
                            </div>
                        </form>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->

                @if (Model.IsSearched)
                {
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Kết quả tìm kiếm (@Model.Products.Count())</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            @if (Model.Products.Any())
                            {
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th style="width: 10px">#</th>
                                            <th>Tên khóa học</th>
                                            <th>Giá</th>
                                            <th>Danh mục</th>
                                            <th>Cấp độ</th>
                                            <th style="width: 150px">Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.Products)
                                        {
                                            <tr>
                                                <td>@item.Id</td>
                                                <td>@item.Name</td>
                                                <td>@item.Price.ToString("N0") VNĐ</td>
                                                <td>@item.Category?.Name</td>
                                                <td>@item.Level</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm" title="Xem chi tiết">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            }
                            else
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> Không tìm thấy khóa học nào phù hợp với tiêu chí tìm kiếm.
                                </div>
                            }
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                }
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->