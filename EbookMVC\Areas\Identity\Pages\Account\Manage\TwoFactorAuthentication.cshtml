@page
@model TwoFactorAuthenticationModel
@{
    ViewData["Title"] = "X<PERSON>c thực 2 bước";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-shield-alt"></i>
            <PERSON><PERSON><PERSON> thực 2 bước (2FA)
        </h3>
    </div>
    <div class="card-body">
        <partial name="_StatusMessage" for="StatusMessage" />

        @if (Model.Is2faEnabled)
        {
            if (Model.RecoveryCodesLeft == 0)
            {
                <div class="alert alert-danger">
                    <strong>Bạn không còn mã khôi phục nào.</strong>
                    <p>Bạn phải <a asp-page="./GenerateRecoveryCodes">tạo một bộ mã khôi phục mới</a> trướ<PERSON> khi có thể đăng nhập bằng mã khôi phục.</p>
                </div>
            }
            else if (Model.RecoveryCodesLeft == 1)
            {
                <div class="alert alert-danger">
                    <strong>Bạn còn 1 mã khôi phục.</strong>
                    <p>Bạn có thể <a asp-page="./GenerateRecoveryCodes">tạo một bộ mã khôi phục mới</a>.</p>
                </div>
            }
            else if (Model.RecoveryCodesLeft <= 3)
            {
                <div class="alert alert-warning">
                    <strong>Bạn còn @Model.RecoveryCodesLeft mã khôi phục.</strong>
                    <p>Bạn nên <a asp-page="./GenerateRecoveryCodes">tạo một bộ mã khôi phục mới</a>.</p>
                </div>
            }

            if (Model.IsMachineRemembered)
            {
                <form method="post" style="display: inline-block">
                    <button type="submit" asp-page-handler="ForgetBrowser" class="btn btn-primary">Quên trình duyệt này</button>
                </form>
            }
            <a asp-page="./Disable2fa" class="btn btn-primary">Tắt 2FA</a>
            <a asp-page="./GenerateRecoveryCodes" class="btn btn-primary">Đặt lại mã khôi phục</a>
        }

        <h4>Ứng dụng xác thực</h4>
        @if (!Model.HasAuthenticator)
        {
            <a id="enable-authenticator" asp-page="./EnableAuthenticator" class="btn btn-primary">Thêm ứng dụng xác thực</a>
        }
        else
        {
            <a id="enable-authenticator" asp-page="./EnableAuthenticator" class="btn btn-primary">Thiết lập ứng dụng xác thực</a>
            <a id="reset-authenticator" asp-page="./ResetAuthenticator" class="btn btn-primary">Đặt lại ứng dụng xác thực</a>
        }
    </div>
</div>
