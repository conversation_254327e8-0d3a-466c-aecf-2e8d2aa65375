@{
    ViewData["Title"] = "Test Phân quyền";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<div class="login-box">
    <div class="card card-outline card-primary">
        <div class="card-header text-center">
            <a href="/" class="h1"><b>Elearn</b> Test</a>
        </div>
        <div class="card-body">
            <h4 class="text-center mb-4">Test Phân quyền Admin</h4>
            <p class="text-center mb-4">
                Trang này để test phân quyền Admin. Click vào nút bên dưới để test:
            </p>
            
            <div class="text-center">
                <div class="btn-group-vertical w-100">
                    <!-- Test Admin Access -->
                    <a href="@Url.Action("AdminTest", "Test")" class="btn btn-danger btn-block mb-2">
                        <i class="fas fa-shield-alt"></i> Test Admin Access
                    </a>
                    
                    <!-- Đăng nhập -->
                    <a href="/Identity/Account/Login" class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-sign-in-alt"></i> Đăng nhập
                    </a>
                    
                    <!-- Đăng ký -->
                    <a href="/Identity/Account/Register" class="btn btn-success btn-block">
                        <i class="fas fa-user-plus"></i> Đăng ký
                    </a>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <small class="text-muted">
                    Nếu bạn chưa có tài khoản Admin, vui lòng đăng ký và liên hệ quản trị viên để cấp quyền.
                </small>
            </div>
        </div>
        <!-- /.card-body -->
    </div>
    <!-- /.card -->
</div>
<!-- /.login-box -->
