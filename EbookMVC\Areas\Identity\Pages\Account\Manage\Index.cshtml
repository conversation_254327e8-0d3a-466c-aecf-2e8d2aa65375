@page
@model IndexModel
@{
    ViewData["Title"] = "Thông tin cá nhân";
    ViewData["ActivePage"] = ManageNavPages.Index;
}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-user"></i>
            Cậ<PERSON> nhật thông tin cá nhân
        </h3>
    </div>
    <div class="card-body">
        <partial name="_StatusMessage" for="StatusMessage" />
        <form id="profile-form" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

            <div class="form-group">
                <label asp-for="Username" class="form-label">Tên đăng nhập</label>
                <input asp-for="Username" class="form-control" placeholder="Vui lòng chọn tên đăng nhập của bạn." disabled />
            </div>

            <div class="form-group">
                <label asp-for="Input.Email" class="form-label">Email</label>
                <input asp-for="Input.Email" class="form-control" placeholder="Vui lòng nhập email của bạn." />
                <span asp-validation-for="Input.Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Input.PhoneNumber" class="form-label">Số điện thoại</label>
                <input asp-for="Input.PhoneNumber" class="form-control" placeholder="Vui lòng nhập số điện thoại của bạn." />
                <span asp-validation-for="Input.PhoneNumber" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Input.FullName" class="form-label">Họ và tên</label>
                <input asp-for="Input.FullName" class="form-control" placeholder="Vui lòng nhập họ và tên của bạn." />
                <span asp-validation-for="Input.FullName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <button id="update-profile-button" type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Lưu thay đổi
                </button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
