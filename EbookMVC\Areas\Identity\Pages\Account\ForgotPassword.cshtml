@page
@model ForgotPasswordModel
@{
    ViewData["Title"] = "Quên mật khẩu";
}

<div class="login-box">
    <div class="card card-outline card-primary">
        <div class="card-header">
            <a href="@Url.Action("Index", "Home")" class="link-dark text-center link-offset-2 link-opacity-100 link-opacity-50-hover">
                <h1 class="mb-0"><b>E</b>learn</h1>
            </a>
        </div>
        <div class="card-body login-card-body">
            <p class="login-box-msg">Bạn quên mật khẩu? Nhập email để đặt lại mật khẩu.</p>

            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

            <form method="post">
                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Email" />
                        <label asp-for="Input.Email">Email</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-envelope"></span></div>
                </div>
                <span asp-validation-for="Input.Email" class="text-danger"></span>

                <div class="row">
                    <div class="col-12">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Gửi yêu cầu đặt lại mật khẩu</button>
                        </div>
                    </div>
                </div>
            </form>

            <p class="mb-1 mt-3">
                <a href="@Url.Page("./Login")" class="link-primary">Quay lại đăng nhập</a>
            </p>
            <p class="mb-0">
                <a href="@Url.Page("./Register")" class="link-primary">Đăng ký tài khoản mới</a>
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
