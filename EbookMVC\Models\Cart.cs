using System.ComponentModel.DataAnnotations;

namespace EbookMVC.Models
{
    public class Cart
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; }

        public ApplicationUser User { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime UpdatedDate { get; set; } = DateTime.Now;

        public List<CartItem> CartItems { get; set; } = new List<CartItem>();

        // Tính tổng số lượng sản phẩm trong giỏ hàng
        public int TotalItems => CartItems?.Sum(item => item.Quantity) ?? 0;

        // Tính tổng giá trị giỏ hàng
        public decimal TotalAmount => CartItems?.Sum(item => item.TotalPrice) ?? 0;
    }
}
