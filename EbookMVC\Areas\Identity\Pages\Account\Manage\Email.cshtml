@page
@model EmailModel
@{
    ViewData["Title"] = "Quản lý Email";
    ViewData["ActivePage"] = ManageNavPages.Email;
}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-envelope"></i>
            Quản lý Email
        </h3>
    </div>
    <div class="card-body">
        <partial name="_StatusMessage" for="StatusMessage" />
        <form id="email-form" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

            @if (Model.IsEmailConfirmed)
            {
                <div class="form-group">
                    <label class="form-label"><PERSON>ail hiện tại</label>
                    <div class="input-group">
                        <input class="form-control" value="@Model.Email" disabled />
                        <div class="input-group-text text-success">
                            <i class="fas fa-check-circle"></i>
                            <PERSON><PERSON> x<PERSON><PERSON> thực
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="form-group">
                    <label class="form-label"><PERSON><PERSON> hiện tại</label>
                    <div class="input-group">
                        <input class="form-control" value="@Model.Email" disabled />
                        <div class="input-group-text text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Chưa xác thực
                        </div>
                    </div>
                    <button id="email-verification" type="submit" asp-page-handler="SendVerificationEmail" class="btn btn-link">
                        Gửi email xác thực
                    </button>
                </div>
            }

            <div class="form-group">
                <label asp-for="Input.NewEmail" class="form-label">Email mới</label>
                <input asp-for="Input.NewEmail" class="form-control" autocomplete="email" aria-required="true" placeholder="Vui lòng nhập email mới." />
                <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
            </div>

            <div class="form-group">
                <button id="change-email-button" type="submit" asp-page-handler="ChangeEmail" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Thay đổi email
                </button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
