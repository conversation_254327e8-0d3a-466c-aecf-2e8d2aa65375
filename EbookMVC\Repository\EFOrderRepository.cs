using EbookMVC.Models;
using Microsoft.EntityFrameworkCore;

namespace EbookMVC.Repository
{
    public class EFOrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;

        public EFOrderRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public Order GetById(int id)
        {
            var order = _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                        .ThenInclude(p => p.Category)
                .FirstOrDefault(o => o.Id == id);

            if (order == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy đơn hàng có ID: {id}");
            }

            return order;
        }

        public Order GetByOrderNumber(string orderNumber)
        {
            return _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                        .ThenInclude(p => p.Category)
                .FirstOrDefault(o => o.OrderNumber == orderNumber)
                ?? throw new KeyNotFoundException($"Không tìm thấy đơn hàng có mã: {orderNumber}");
        }

        public IEnumerable<Order> GetAll()
        {
            return _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                .OrderByDescending(o => o.OrderDate)
                .ToList();
        }

        public IEnumerable<Order> GetByUserId(string userId)
        {
            return _context.Orders
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                        .ThenInclude(p => p.Category)
                .Where(o => o.UserId == userId)
                .OrderByDescending(o => o.OrderDate)
                .ToList();
        }

        public void Add(Order order)
        {
            if (string.IsNullOrEmpty(order.OrderNumber))
            {
                order.OrderNumber = GenerateOrderNumber();
            }
            
            _context.Orders.Add(order);
            _context.SaveChanges();
        }

        public void Update(Order order)
        {
            order.UpdatedDate = DateTime.Now;
            _context.Orders.Update(order);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var order = _context.Orders.Find(id);
            if (order != null)
            {
                _context.Orders.Remove(order);
                _context.SaveChanges();
            }
        }

        public void UpdateOrderStatus(int orderId, OrderStatus status)
        {
            UpdateOrderStatus(orderId, status, null);
        }

        public void UpdateOrderStatus(int orderId, OrderStatus status, string adminNotes)
        {
            var order = _context.Orders.Find(orderId);
            if (order != null)
            {
                order.Status = status;
                order.UpdatedDate = DateTime.Now;
                
                if (!string.IsNullOrEmpty(adminNotes))
                {
                    order.AdminNotes = adminNotes;
                }

                // Cập nhật ngày giao hàng nếu trạng thái là Delivered
                if (status == OrderStatus.Delivered && !order.DeliveredDate.HasValue)
                {
                    order.DeliveredDate = DateTime.Now;
                }

                _context.SaveChanges();
            }
        }

        public IEnumerable<Order> GetOrdersByStatus(OrderStatus status)
        {
            return _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                .Where(o => o.Status == status)
                .OrderByDescending(o => o.OrderDate)
                .ToList();
        }

        public IEnumerable<Order> GetOrdersByDateRange(DateTime startDate, DateTime endDate)
        {
            return _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .OrderByDescending(o => o.OrderDate)
                .ToList();
        }

        public IEnumerable<Order> SearchOrders(string keyword, OrderStatus? status = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                .AsQueryable();

            // Tìm kiếm theo từ khóa
            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(o =>
                    o.OrderNumber.Contains(keyword) ||
                    o.ShippingName.Contains(keyword) ||
                    o.ShippingPhone.Contains(keyword) ||
                    o.ShippingEmail.Contains(keyword) ||
                    (o.User != null && o.User.UserName != null && o.User.UserName.Contains(keyword)) ||
                    (o.User != null && o.User.Email != null && o.User.Email.Contains(keyword)));
            }

            // Lọc theo trạng thái
            if (status.HasValue)
            {
                query = query.Where(o => o.Status == status.Value);
            }

            // Lọc theo ngày
            if (startDate.HasValue)
            {
                query = query.Where(o => o.OrderDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(o => o.OrderDate <= endDate.Value);
            }

            return query.OrderByDescending(o => o.OrderDate).ToList();
        }

        public int GetTotalOrdersCount()
        {
            return _context.Orders.Count();
        }

        public int GetOrdersCountByStatus(OrderStatus status)
        {
            return _context.Orders.Count(o => o.Status == status);
        }

        public decimal GetTotalRevenue()
        {
            return _context.Orders
                .Where(o => o.IsPaid && o.Status != OrderStatus.Cancelled && o.Status != OrderStatus.Refunded)
                .Sum(o => o.TotalAmount);
        }

        public decimal GetRevenueByDateRange(DateTime startDate, DateTime endDate)
        {
            return _context.Orders
                .Where(o => o.IsPaid && 
                           o.Status != OrderStatus.Cancelled && 
                           o.Status != OrderStatus.Refunded &&
                           o.OrderDate >= startDate && 
                           o.OrderDate <= endDate)
                .Sum(o => o.TotalAmount);
        }

        public Dictionary<OrderStatus, int> GetOrderStatusCounts()
        {
            return _context.Orders
                .GroupBy(o => o.Status)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        public IEnumerable<OrderItem> GetOrderItems(int orderId)
        {
            return _context.OrderItems
                .Include(oi => oi.Product)
                    .ThenInclude(p => p.Category)
                .Where(oi => oi.OrderId == orderId)
                .ToList();
        }

        public void AddOrderItem(OrderItem orderItem)
        {
            _context.OrderItems.Add(orderItem);
            _context.SaveChanges();
        }

        public void UpdateOrderItem(OrderItem orderItem)
        {
            _context.OrderItems.Update(orderItem);
            _context.SaveChanges();
        }

        public void RemoveOrderItem(int orderItemId)
        {
            var orderItem = _context.OrderItems.Find(orderItemId);
            if (orderItem != null)
            {
                _context.OrderItems.Remove(orderItem);
                _context.SaveChanges();
            }
        }

        public IEnumerable<Order> GetRecentOrders(int count = 10)
        {
            return _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderItems)
                .OrderByDescending(o => o.OrderDate)
                .Take(count)
                .ToList();
        }

        public IEnumerable<Order> GetRecentOrdersByUser(string userId, int count = 10)
        {
            return _context.Orders
                .Include(o => o.OrderItems)
                    .ThenInclude(oi => oi.Product)
                .Where(o => o.UserId == userId)
                .OrderByDescending(o => o.OrderDate)
                .Take(count)
                .ToList();
        }

        public string GenerateOrderNumber()
        {
            var today = DateTime.Now;
            var prefix = $"ORD{today:yyyyMMdd}";
            
            var lastOrder = _context.Orders
                .Where(o => o.OrderNumber.StartsWith(prefix))
                .OrderByDescending(o => o.OrderNumber)
                .FirstOrDefault();

            int sequence = 1;
            if (lastOrder != null)
            {
                var lastSequence = lastOrder.OrderNumber.Substring(prefix.Length);
                if (int.TryParse(lastSequence, out int lastSeq))
                {
                    sequence = lastSeq + 1;
                }
            }

            return $"{prefix}{sequence:D4}";
        }

        public void MarkAsPaid(int orderId, string transactionId, string paymentMethod)
        {
            var order = _context.Orders.Find(orderId);
            if (order != null)
            {
                order.IsPaid = true;
                order.PaidDate = DateTime.Now;
                order.PaymentDate = DateTime.Now;
                order.TransactionId = transactionId;
                order.PaymentMethod = paymentMethod;
                order.UpdatedDate = DateTime.Now;

                _context.SaveChanges();
            }
        }

        public void MarkAsUnpaid(int orderId)
        {
            var order = _context.Orders.Find(orderId);
            if (order != null)
            {
                order.IsPaid = false;
                order.PaidDate = null;
                order.PaymentDate = null;
                order.TransactionId = null;
                order.UpdatedDate = DateTime.Now;

                _context.SaveChanges();
            }
        }
    }
}
