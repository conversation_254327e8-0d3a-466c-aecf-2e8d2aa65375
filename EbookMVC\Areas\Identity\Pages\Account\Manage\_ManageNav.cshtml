@using Microsoft.AspNetCore.Identity
@inject SignInManager<ApplicationUser> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-user-cog"></i>
            Quản lý tài khoản
        </h3>
    </div>
    <div class="card-body p-0">
        <ul class="nav nav-pills flex-column manage-nav">
            <li class="nav-item">
                <a class="nav-link @ManageNavPages.IndexNavClass(ViewContext)" id="profile" asp-page="./Index">
                    <i class="fas fa-user"></i>
                    Thông tin cá nhân
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ManageNavPages.EmailNavClass(ViewContext)" id="email" asp-page="./Email">
                    <i class="fas fa-envelope"></i>
                    Email
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ManageNavPages.ChangePasswordNavClass(ViewContext)" id="change-password" asp-page="./ChangePassword">
                    <i class="fas fa-key"></i>
                    Đổi mật khẩu
                </a>
            </li>
            @if (hasExternalLogins)
            {
                <li class="nav-item">
                    <a class="nav-link @ManageNavPages.ExternalLoginsNavClass(ViewContext)" id="external-login" asp-page="./ExternalLogins">
                        <i class="fas fa-link"></i>
                        Đăng nhập bên ngoài
                    </a>
                </li>
            }
            <li class="nav-item">
                <a class="nav-link @ManageNavPages.TwoFactorAuthenticationNavClass(ViewContext)" id="two-factor" asp-page="./TwoFactorAuthentication">
                    <i class="fas fa-shield-alt"></i>
                    Xác thực 2 bước
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ManageNavPages.PersonalDataNavClass(ViewContext)" id="personal-data" asp-page="./PersonalData">
                    <i class="fas fa-download"></i>
                    Dữ liệu cá nhân
                </a>
            </li>
        </ul>
    </div>
</div>
