@page
@model PersonalDataModel
@{
    ViewData["Title"] = "Dữ liệu cá nhân";
    ViewData["ActivePage"] = ManageNavPages.PersonalData;
}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-download"></i>
            Dữ liệu cá nhân
        </h3>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Thông tin!</h5>
            Tài khoản của bạn chứa dữ liệu cá nhân mà bạn đã cung cấp cho chúng tôi. Trang này cho phép bạn tải xuống hoặc xóa dữ liệu đó.
            <strong>Việc xóa dữ liệu này sẽ xóa vĩnh viễn tài khoản của bạn và không thể khôi phục.</strong>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card card-outline card-primary">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-download"></i>
                            Tải xuống dữ liệu
                        </h3>
                    </div>
                    <div class="card-body">
                        <p>Nhấp vào nút bên dưới để tải xuống tệp chứa tất cả dữ liệu cá nhân của bạn.</p>
                        <form method="post" asp-page-handler="DownloadPersonalData">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-download"></i>
                                Tải xuống
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card card-outline card-danger">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-trash"></i>
                            Xóa tài khoản
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-danger">
                            <strong>Cảnh báo:</strong> Việc xóa tài khoản sẽ xóa vĩnh viễn tất cả dữ liệu của bạn và không thể khôi phục.
                        </p>
                        <a asp-page="./DeletePersonalData" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            Xóa tài khoản
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
