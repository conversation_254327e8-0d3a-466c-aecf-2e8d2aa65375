﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="login-box">
    <div class="card card-outline card-primary">
        <div class="card-header">
            <a href="@Url.Action("Index", "Home")" class="link-dark text-center link-offset-2 link-opacity-100 link-opacity-50-hover">
                <h1 class="mb-0"><b>E</b>learn</h1>
            </a>
        </div>
        <div class="card-body login-card-body">
            <p class="login-box-msg">Đăng nhập để bắt đầu phiên làm việc</p>

            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

            <form id="account" method="post">
                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Email" />
                        <label asp-for="Input.Email">Email</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-envelope"></span></div>
                </div>
                <span asp-validation-for="Input.Email" class="text-danger"></span>

                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Mật khẩu" />
                        <label asp-for="Input.Password">Mật khẩu</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-lock-fill"></span></div>
                </div>
                <span asp-validation-for="Input.Password" class="text-danger"></span>

                <!--begin::Row-->
                <div class="row">
                    <div class="col-8 d-inline-flex align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            <label class="form-check-label" asp-for="Input.RememberMe">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>
                    </div>
                    <!-- /.col -->
                    <div class="col-4">
                        <div class="d-grid gap-2">
                            <button id="login-submit" type="submit" class="btn btn-primary">Đăng nhập</button>
                        </div>
                    </div>
                    <!-- /.col -->
                </div>
                <!--end::Row-->
            </form>

            @{
                if ((Model.ExternalLogins?.Count ?? 0) > 0)
                {
                    <div class="social-auth-links text-center mb-3 d-grid gap-2">
                        <p>- HOẶC -</p>
                        @foreach (var provider in Model.ExternalLogins!)
                        {
                            <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post">
                                <button type="submit" class="btn btn-primary" name="provider" value="@provider.Name" title="Đăng nhập bằng @provider.DisplayName">
                                    <i class="bi bi-@(provider.Name.ToLower()) me-2"></i> Đăng nhập bằng @provider.DisplayName
                                </button>
                            </form>
                        }
                    </div>
                }
            }

            <!-- /.social-auth-links -->
            <p class="mb-1"><a href="@Url.Page("./ForgotPassword")" class="link-primary">Quên mật khẩu?</a></p>
            <p class="mb-0">
                <a href="@Url.Page("./Register", new { ReturnUrl = Model.ReturnUrl })" class="link-primary text-center">Đăng ký tài khoản mới</a>
            </p>
        </div>
        <!-- /.login-card-body -->
    </div>
</div>
<!-- /.login-box -->

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
