@model EbookMVC.Models.Category

@{
    ViewData["Title"] = "Chi tiết danh mục";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a asp-action="Index">Danh mục khóa học</a></li>
                    <li class="breadcrumb-item active">Chi tiết</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Thông tin danh mục</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="remove">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">@Html.DisplayNameFor(model => model.Id)</dt>
                            <dd class="col-sm-8">@Html.DisplayFor(model => model.Id)</dd>

                            <dt class="col-sm-4">@Html.DisplayNameFor(model => model.Name)</dt>
                            <dd class="col-sm-8">@Html.Raw(Model.Name)</dd>

                            <dt class="col-sm-4">@Html.DisplayNameFor(model => model.Description)</dt>
                            <dd class="col-sm-8">@Html.Raw(Model.Description)</dd>
                        </dl>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Khóa học thuộc danh mục này</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="remove">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        @if (ViewBag.Products != null && ViewBag.Products.Count > 0)
                        {
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Tên khóa học</th>
                                        <th>Giá</th>
                                        <th>Cấp độ</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in ViewBag.Products)
                                    {
                                        <tr>
                                            <td>@item.Id</td>
                                            <td>@item.Name</td>
                                            <td>@item.Price.ToString("N0") VNĐ</td>
                                            <td>@item.Level</td>
                                            <td>
                                                <a asp-controller="Product" asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Không có khóa học nào thuộc danh mục này.
                            </div>
                        }
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
