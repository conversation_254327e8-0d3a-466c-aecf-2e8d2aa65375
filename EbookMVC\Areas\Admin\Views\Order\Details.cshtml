@model EbookMVC.Models.Order
@using EbookMVC.Models

@{
    ViewData["Title"] = $"Chi tiết đơn hàng #{Model?.OrderNumber ?? "N/A"}";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Chi tiết đơn hàng</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin/Home">Home</a></li>
                    <li class="breadcrumb-item"><a asp-action="Index">Quản lý đơn hàng</a></li>
                    <li class="breadcrumb-item active">@Model.OrderNumber</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Thông báo -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <!-- Order Header -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice"></i> Đơn hàng #@Model.OrderNumber
                        </h3>
                        <div class="card-tools">
                            <span class="badge @Model.StatusBadgeClass badge-lg">@Model.StatusDisplayName</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-info-circle"></i> Thông tin đơn hàng</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Mã đơn hàng:</strong></td>
                                        <td>@Model.OrderNumber</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ngày đặt:</strong></td>
                                        <td>@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Cập nhật lần cuối:</strong></td>
                                        <td>@Model.UpdatedDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Khách hàng:</strong></td>
                                        <td>@Model.User?.Email</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-shipping-fast"></i> Thông tin giao hàng</h5>
                                <address>
                                    <strong>@Model.ShippingName</strong><br>
                                    @Model.ShippingAddress<br>
                                    @if (!string.IsNullOrEmpty(Model.ShippingCity))
                                    {
                                        @Model.ShippingCity<br>
                                    }
                                    <abbr title="Phone">SĐT:</abbr> @Model.ShippingPhone<br>
                                    <abbr title="Email">Email:</abbr> @Model.ShippingEmail
                                </address>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Notes))
                        {
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6><i class="fas fa-sticky-note"></i> Ghi chú từ khách hàng</h6>
                                    <p class="text-muted">@Model.Notes</p>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.AdminNotes))
                        {
                            <div class="row">
                                <div class="col-12">
                                    <h6><i class="fas fa-user-shield"></i> Ghi chú từ admin</h6>
                                    <p class="text-info">@Model.AdminNotes</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shopping-bag"></i> Sản phẩm trong đơn hàng
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Sản phẩm</th>
                                        <th>Số lượng</th>
                                        <th>Đơn giá</th>
                                        <th>Thành tiền</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.OrderItems)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                                    {
                                                        <img src="@item.ProductImageUrl" alt="@item.ProductName" class="img-thumbnail mr-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-light d-flex align-items-center justify-content-center mr-3" style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    }
                                                    <div>
                                                        <strong>@item.ProductName</strong>
                                                        @if (!string.IsNullOrEmpty(item.CategoryName))
                                                        {
                                                            <br><span class="badge badge-secondary">@item.CategoryName</span>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center">@item.Quantity</td>
                                            <td class="text-right">@item.UnitPrice.ToString("N0") VNĐ</td>
                                            <td class="text-right"><strong>@item.TotalPrice.ToString("N0") VNĐ</strong></td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-right"><strong>Tạm tính:</strong></td>
                                        <td class="text-right">@Model.TotalAmount.ToString("N0") VNĐ</td>
                                    </tr>
                                    @if (Model.ShippingFee > 0)
                                    {
                                        <tr>
                                            <td colspan="3" class="text-right"><strong>Phí vận chuyển:</strong></td>
                                            <td class="text-right">@Model.ShippingFee.ToString("N0") VNĐ</td>
                                        </tr>
                                    }
                                    @if (Model.Tax > 0)
                                    {
                                        <tr>
                                            <td colspan="3" class="text-right"><strong>Thuế:</strong></td>
                                            <td class="text-right">@Model.Tax.ToString("N0") VNĐ</td>
                                        </tr>
                                    }
                                    <tr class="bg-light">
                                        <td colspan="3" class="text-right"><strong>Tổng cộng:</strong></td>
                                        <td class="text-right"><strong class="text-success h5">@Model.GrandTotal.ToString("N0") VNĐ</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Order Status Management -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs"></i> Quản lý đơn hàng
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Update Status -->
                        <form asp-action="UpdateStatus" method="post">
                            <input type="hidden" name="orderId" value="@Model.Id" />
                            <div class="form-group">
                                <label>Cập nhật trạng thái:</label>
                                <select name="status" class="form-control">
                                    @foreach (EbookMVC.Models.OrderStatus status in Enum.GetValues<EbookMVC.Models.OrderStatus>())
                                    {
                                        <option value="@status" selected="@(status == Model.Status)">@status.GetDisplayName()</option>
                                    }
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Ghi chú admin:</label>
                                <textarea name="adminNotes" class="form-control" rows="3" placeholder="Ghi chú về việc cập nhật trạng thái...">@Model.AdminNotes</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-save"></i> Cập nhật trạng thái
                            </button>
                        </form>

                        <hr>

                        <!-- Payment Management -->
                        <h6><i class="fas fa-credit-card"></i> Quản lý thanh toán</h6>
                        <p><strong>Trạng thái:</strong> 
                            @if (Model.IsPaid)
                            {
                                <span class="badge badge-success">Đã thanh toán</span>
                            }
                            else
                            {
                                <span class="badge badge-warning">Chưa thanh toán</span>
                            }
                        </p>
                        <p><strong>Phương thức:</strong> @Model.PaymentMethod</p>
                        @if (!string.IsNullOrEmpty(Model.TransactionId))
                        {
                            <p><strong>Mã giao dịch:</strong> @Model.TransactionId</p>
                        }

                        @if (!Model.IsPaid)
                        {
                            <form asp-action="MarkAsPaid" method="post" class="mb-2">
                                <input type="hidden" name="orderId" value="@Model.Id" />
                                <div class="form-group">
                                    <input type="text" name="transactionId" class="form-control" placeholder="Mã giao dịch" />
                                </div>
                                <div class="form-group">
                                    <select name="paymentMethod" class="form-control">
                                        <option value="COD">Thanh toán khi nhận hàng</option>
                                        <option value="BANK_TRANSFER">Chuyển khoản ngân hàng</option>
                                        <option value="MOMO">Ví điện tử MoMo</option>
                                        <option value="CASH">Tiền mặt</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-check"></i> Đánh dấu đã thanh toán
                                </button>
                            </form>
                        }
                        else
                        {
                            <form asp-action="MarkAsUnpaid" method="post">
                                <input type="hidden" name="orderId" value="@Model.Id" />
                                <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Bạn có chắc chắn muốn đánh dấu chưa thanh toán?')">
                                    <i class="fas fa-times"></i> Đánh dấu chưa thanh toán
                                </button>
                            </form>
                        }
                    </div>
                    <div class="card-footer">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                        @if (Model.Status == EbookMVC.Models.OrderStatus.Cancelled)
                        {
                            <form asp-action="Delete" asp-route-id="@Model.Id" method="post" style="display: inline;" 
                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')">
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> Xóa đơn hàng
                                </button>
                            </form>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
