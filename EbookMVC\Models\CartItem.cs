using System.ComponentModel.DataAnnotations;

namespace EbookMVC.Models
{
    public class CartItem
    {
        public int Id { get; set; }

        [Required]
        public int CartId { get; set; }

        public Cart Cart { get; set; }

        [Required]
        public int ProductId { get; set; }

        public Product Product { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
        public int Quantity { get; set; } = 1;

        [Required]
        public decimal UnitPrice { get; set; }

        public DateTime AddedDate { get; set; } = DateTime.Now;

        // Tính tổng giá cho item này
        public decimal TotalPrice => Quantity * UnitPrice;
    }
}
