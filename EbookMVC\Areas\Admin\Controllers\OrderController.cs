using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EbookMVC.Models;
using EbookMVC.Repository;
using EbookMVC.Areas.Admin.Models;

namespace EbookMVC.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = SD.Role_Admin)]
    public class OrderController : Controller
    {
        private readonly IOrderRepository _orderRepository;
        private readonly IUserLogRepository _userLogRepository;

        public OrderController(
            IOrderRepository orderRepository,
            IUserLogRepository userLogRepository)
        {
            _orderRepository = orderRepository;
            _userLogRepository = userLogRepository;
        }

        // GET: Admin/Order
        public IActionResult Index(string searchKeyword, OrderStatus? status, DateTime? startDate, DateTime? endDate)
        {
            IEnumerable<Order> orders;

            if (!string.IsNullOrEmpty(searchKeyword) || status.HasValue || startDate.HasValue || endDate.HasValue)
            {
                orders = _orderRepository.SearchOrders(searchKeyword, status, startDate, endDate);
            }
            else
            {
                orders = _orderRepository.GetAll();
            }

            ViewBag.SearchKeyword = searchKeyword;
            ViewBag.Status = status;
            ViewBag.StartDate = startDate?.ToString("yyyy-MM-dd");
            ViewBag.EndDate = endDate?.ToString("yyyy-MM-dd");
            ViewBag.OrderStatuses = Enum.GetValues<OrderStatus>();

            return View(orders);
        }

        // GET: Admin/Order/Details/5
        public IActionResult Details(int id)
        {
            try
            {
                var order = _orderRepository.GetById(id);
                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                    return RedirectToAction("Index");
                }

                return View(order);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // POST: Admin/Order/UpdateStatus
        [HttpPost]
        public IActionResult UpdateStatus(int orderId, OrderStatus status, string adminNotes)
        {
            try
            {
                var order = _orderRepository.GetById(orderId);
                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                    return RedirectToAction("Index");
                }

                var oldStatus = order.Status;
                _orderRepository.UpdateOrderStatus(orderId, status, adminNotes);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "UPDATE_ORDER_STATUS",
                    EntityName = "Order",
                    EntityId = orderId,
                    Description = $"Cập nhật trạng thái đơn hàng {order.OrderNumber} từ '{oldStatus.GetDisplayName()}' thành '{status.GetDisplayName()}'",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = $"Đã cập nhật trạng thái đơn hàng thành '{status.GetDisplayName()}'";
                return RedirectToAction("Details", new { id = orderId });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Details", new { id = orderId });
            }
        }

        // POST: Admin/Order/MarkAsPaid
        [HttpPost]
        public IActionResult MarkAsPaid(int orderId, string transactionId, string paymentMethod)
        {
            try
            {
                var order = _orderRepository.GetById(orderId);
                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                    return RedirectToAction("Index");
                }

                _orderRepository.MarkAsPaid(orderId, transactionId, paymentMethod);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "MARK_ORDER_PAID",
                    EntityName = "Order",
                    EntityId = orderId,
                    Description = $"Đánh dấu đơn hàng {order.OrderNumber} đã thanh toán - Phương thức: {paymentMethod}",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = "Đã đánh dấu đơn hàng đã thanh toán!";
                return RedirectToAction("Details", new { id = orderId });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Details", new { id = orderId });
            }
        }

        // POST: Admin/Order/MarkAsUnpaid
        [HttpPost]
        public IActionResult MarkAsUnpaid(int orderId)
        {
            try
            {
                var order = _orderRepository.GetById(orderId);
                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                    return RedirectToAction("Index");
                }

                _orderRepository.MarkAsUnpaid(orderId);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "MARK_ORDER_UNPAID",
                    EntityName = "Order",
                    EntityId = orderId,
                    Description = $"Đánh dấu đơn hàng {order.OrderNumber} chưa thanh toán",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = "Đã đánh dấu đơn hàng chưa thanh toán!";
                return RedirectToAction("Details", new { id = orderId });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Details", new { id = orderId });
            }
        }

        // GET: Admin/Order/Statistics
        public IActionResult Statistics()
        {
            var statusCounts = _orderRepository.GetOrderStatusCounts();
            var totalRevenue = _orderRepository.GetTotalRevenue();
            var totalOrders = _orderRepository.GetTotalOrdersCount();

            var viewModel = new
            {
                StatusCounts = statusCounts,
                TotalRevenue = totalRevenue,
                TotalOrders = totalOrders,
                RecentOrders = _orderRepository.GetRecentOrders(10)
            };

            return View(viewModel);
        }

        // DELETE: Admin/Order/Delete/5
        [HttpPost]
        public IActionResult Delete(int id)
        {
            try
            {
                var order = _orderRepository.GetById(id);
                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                    return RedirectToAction("Index");
                }

                // Chỉ cho phép xóa đơn hàng đã hủy
                if (order.Status != OrderStatus.Cancelled)
                {
                    TempData["ErrorMessage"] = "Chỉ có thể xóa đơn hàng đã hủy!";
                    return RedirectToAction("Details", new { id = id });
                }

                _orderRepository.Delete(id);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "DELETE_ORDER",
                    EntityName = "Order",
                    EntityId = id,
                    Description = $"Xóa đơn hàng {order.OrderNumber}",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = "Đã xóa đơn hàng thành công!";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra khi xóa đơn hàng: {ex.Message}";
                return RedirectToAction("Details", new { id = id });
            }
        }

        // GET: Admin/Order/CreateSampleData - Tạo dữ liệu mẫu để test
        public IActionResult CreateSampleData()
        {
            try
            {
                // Kiểm tra xem đã có đơn hàng nào chưa
                if (_orderRepository.GetAll().Any())
                {
                    TempData["ErrorMessage"] = "Đã có dữ liệu đơn hàng trong hệ thống!";
                    return RedirectToAction("Index");
                }

                // Tạo đơn hàng mẫu
                var sampleOrder = new Order
                {
                    OrderNumber = _orderRepository.GenerateOrderNumber(),
                    UserId = User.Identity?.Name ?? "<EMAIL>",
                    OrderDate = DateTime.Now.AddDays(-2),
                    Status = OrderStatus.Completed,
                    TotalAmount = 1500000,
                    IsPaid = true,
                    PaidDate = DateTime.Now.AddDays(-1),
                    PaymentMethod = "Credit Card",
                    TransactionId = "TXN" + DateTime.Now.Ticks,
                    ShippingName = "Nguyễn Văn A",
                    ShippingPhone = "0901234567",
                    ShippingEmail = "<EMAIL>",
                    ShippingAddress = "123 Đường ABC, Quận 1, TP.HCM",
                    ShippingCity = "TP.HCM",
                    ShippingPostalCode = "70000",
                    Notes = "Đơn hàng mẫu để test hệ thống",
                    AdminNotes = "Dữ liệu mẫu được tạo tự động",
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    DeliveredDate = DateTime.Now
                };

                // Thêm OrderItem mẫu
                sampleOrder.OrderItems.Add(new OrderItem
                {
                    ProductId = 1, // Giả sử có sản phẩm với ID = 1
                    ProductName = "Khóa học lập trình C# cơ bản",
                    Quantity = 1,
                    UnitPrice = 1500000,
                    CategoryName = "Lập trình",
                    Level = "Cơ bản",
                    Duration = 40
                });

                _orderRepository.Add(sampleOrder);

                TempData["SuccessMessage"] = "Đã tạo dữ liệu mẫu thành công!";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra khi tạo dữ liệu mẫu: {ex.Message}";
                return RedirectToAction("Index");
            }
        }
    }
}
