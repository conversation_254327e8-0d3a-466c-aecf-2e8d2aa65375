@page
@model ResetPasswordModel
@{
    ViewData["Title"] = "Đặt lại mật khẩu";
}

<div class="login-box">
    <div class="card card-outline card-primary">
        <div class="card-header">
            <a href="@Url.Action("Index", "Home")" class="link-dark text-center link-offset-2 link-opacity-100 link-opacity-50-hover">
                <h1 class="mb-0"><b>E</b>learn</h1>
            </a>
        </div>
        <div class="card-body login-card-body">
            <p class="login-box-msg">Đặt lại mật khẩu của bạn</p>

            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

            <form method="post">
                <input asp-for="Input.Code" type="hidden" />
                
                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Email" />
                        <label asp-for="Input.Email">Email</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-envelope"></span></div>
                </div>
                <span asp-validation-for="Input.Email" class="text-danger"></span>

                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Mật khẩu mới" />
                        <label asp-for="Input.Password">Mật khẩu mới</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-lock-fill"></span></div>
                </div>
                <span asp-validation-for="Input.Password" class="text-danger"></span>

                <div class="input-group mb-1">
                    <div class="form-floating">
                        <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Xác nhận mật khẩu mới" />
                        <label asp-for="Input.ConfirmPassword">Xác nhận mật khẩu mới</label>
                    </div>
                    <div class="input-group-text"><span class="bi bi-lock-fill"></span></div>
                </div>
                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>

                <div class="row">
                    <div class="col-12">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Đặt lại mật khẩu</button>
                        </div>
                    </div>
                </div>
            </form>

            <p class="mb-1 mt-3">
                <a href="@Url.Page("./Login")" class="link-primary">Quay lại đăng nhập</a>
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
